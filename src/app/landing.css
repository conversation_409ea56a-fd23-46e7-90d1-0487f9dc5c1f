.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: 1.2;
  font-weight: 700;
}

.gradient-text {
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 4s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Buttons */
.btn-primary,
.btn-secondary {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 14px;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-accent);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(255, 46, 77, 0.3);
}

.btn-secondary {
  background: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, 0.1);
}

.btn-large {
  padding: 16px 32px;
  font-size: 16px;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(10, 10, 10, 0.9);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  z-index: 1000;
  padding: 16px 0;
}

.navbar .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  position: relative;
}

.logo-icon.small {
  width: 32px;
  height: 32px;
}

.persona-layers {
  position: relative;
  width: 100%;
  height: 100%;
}

.persona-layer {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.layer-1 {
  background: linear-gradient(135deg, var(--accent-red), #ff6b7a);
  animation: rotate1 8s linear infinite;
}

.layer-2 {
  background: linear-gradient(135deg, var(--accent-purple), #a855f7);
  transform: scale(0.85) rotate(120deg);
  animation: rotate2 10s linear infinite reverse;
  opacity: 0.8;
}

.layer-3 {
  background: linear-gradient(135deg, var(--accent-cyan), #22d3ee);
  transform: scale(0.7) rotate(240deg);
  animation: rotate3 12s linear infinite;
  opacity: 0.6;
}

@keyframes rotate1 {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes rotate2 {
  from {
    transform: scale(0.85) rotate(120deg);
  }
  to {
    transform: scale(0.85) rotate(480deg);
  }
}

@keyframes rotate3 {
  from {
    transform: scale(0.7) rotate(240deg);
  }
  to {
    transform: scale(0.7) rotate(600deg);
  }
}

.logo-text {
  font-size: 20px;
  font-weight: 900;
  color: var(--text-primary);
}

/* Hero Section */
.hero {
  padding: 120px 0 80px;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    ellipse at center top,
    rgba(255, 46, 77, 0.1) 0%,
    transparent 50%
  );
  pointer-events: none;
}

.hero .container {
  display: grid;
  grid-template-columns: 3fr 1fr;
  gap: 30px;
  align-items: center;
}

.hero-badge {
  display: inline-block;
  background: rgba(255, 46, 77, 0.1);
  color: var(--accent-red);
  padding: 12px 32px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 40px;
  border: 1px solid rgba(255, 46, 77, 0.2);
}

.hero-title {
  font-size: 48px;
  font-weight: 900;
  margin-bottom: 24px;
  line-height: 1.1;
  max-width: 100%;
}

.hero-subtitle {
  font-size: 20px;
  color: var(--text-secondary);
  margin-bottom: 40px;
  line-height: 1.5;
}

.hero-actions {
  display: flex;
  gap: 16px;
  margin-bottom: 60px;
}

.hero-stats {
  display: flex;
  gap: 40px;
}

.stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: 900;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.stat-label {
  font-size: 14px;
  color: var(--text-muted);
}

/* Hero Visual */
.hero-visual {
  position: relative;
  height: 500px;
  overflow: visible;
}

.hero-mockup {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.floating-card {
  position: absolute;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  animation: float 6s ease-in-out infinite;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.floating-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 50px rgba(0, 0, 0, 0.15);
}

.floating-card.card-1 {
  top: 10%;
  left: 5%;
  z-index: 2;
  animation-delay: 0s;
  transform: rotate(-5deg);
}

.floating-card.card-2 {
  top: 25%;
  right: 10%;
  z-index: 1;
  animation-delay: 3s;
  transform: rotate(3deg);
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-15px);
  }
}

.persona-preview {
  display: flex;
  align-items: center;
  gap: 12px;
}

.persona-preview img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.persona-preview h4 {
  font-size: 16px;
  margin-bottom: 4px;
}

.persona-preview p {
  font-size: 12px;
  color: var(--text-muted);
}

/* Social Post Preview Styles */
.social-post-preview {
  width: 280px;
  position: relative;
}

.post-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.post-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--gradient-primary);
}

.post-avatar.foodie {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.post-avatar.travel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.post-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.post-username {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
}

.post-time {
  font-size: 12px;
  color: var(--text-muted);
}

.post-text {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.post-engagement {
  display: flex;
  gap: 16px;
  font-size: 13px;
  color: var(--text-secondary);
}

.post-engagement span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.post-image-container {
  width: 100%;
  margin: 12px 0;
  border-radius: 8px;
  overflow: hidden;
}

img.post-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  display: block;
}

/* AI Generated Card Effects */
.floating-card.ai-generated {
  position: relative;
  overflow: hidden;
  border: 1px solid transparent;
  background-image: linear-gradient(var(--bg-secondary), var(--bg-secondary)),
    linear-gradient(135deg, rgba(124, 58, 237, 0.3), rgba(255, 46, 77, 0.3));
  background-origin: border-box;
  background-clip: padding-box, border-box;
}

.ai-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    rgba(124, 58, 237, 0.1) 0%,
    transparent 70%
  );
  animation: ai-glow-pulse 4s ease-in-out infinite;
  pointer-events: none;
}

@keyframes ai-glow-pulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(0.8);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

.ai-shimmer {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  animation: shimmer 3s ease-in-out infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(200%);
  }
}

.post-avatar {
  position: relative;
}

.ai-pulse {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 12px;
  height: 12px;
  background: var(--accent-purple);
  border-radius: 50%;
  border: 2px solid var(--bg-secondary);
  animation: ai-pulse-ring 2s ease-in-out infinite;
}

.ai-pulse::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid var(--accent-purple);
  transform: translate(-50%, -50%);
  animation: ai-pulse-expand 2s ease-in-out infinite;
}

@keyframes ai-pulse-ring {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes ai-pulse-expand {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(2);
  }
}

.ai-subtle {
  color: var(--accent-purple);
  font-weight: 600;
  font-size: 11px;
  opacity: 0.8;
}

.post-header {
  position: relative;
}

.platform-icons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
}

.source-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  transition: all 0.3s ease;
}

.source-icon:hover {
  border-color: var(--accent-red);
  color: var(--accent-red);
  transform: translateY(-2px);
}

.source-icon svg {
  width: 18px;
  height: 18px;
}

.content-preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-image {
  align-self: flex-start;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.content-preview p {
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.preview-meta {
  font-size: 11px;
  color: var(--text-muted);
  opacity: 0.8;
}

/* Features Section */
.features {
  padding: 100px 0;
  background: var(--bg-secondary);
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-size: 40px;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.feature-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 40px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  transform: translateY(30px);
}

.feature-card.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.feature-card:hover {
  border-color: var(--accent-red);
  transform: translateY(-5px);
  box-shadow: var(--shadow-accent);
}

.feature-card.featured {
  background: linear-gradient(
    135deg,
    rgba(255, 46, 77, 0.05) 0%,
    rgba(124, 58, 237, 0.05) 100%
  );
  border-color: rgba(255, 46, 77, 0.2);
}

.feature-icon {
  width: 60px;
  height: 60px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-personas {
  position: relative;
  width: 100%;
  height: 100%;
}

.mini-persona {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid var(--bg-primary);
}

.mini-persona.p1 {
  background: var(--accent-red);
  top: 0;
  left: 20px;
}

.mini-persona.p2 {
  background: var(--accent-purple);
  top: 20px;
  left: 0;
}

.mini-persona.p3 {
  background: var(--accent-cyan);
  top: 20px;
  right: 0;
}

.icon-camera {
  position: relative;
  width: 50px;
  height: 40px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 2px solid var(--accent-cyan);
}

.camera-lens {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--accent-cyan);
}

.camera-flash {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 12px;
  height: 12px;
  background: var(--accent-red);
  border-radius: 2px;
}

/* Trend Detection Icon */
.icon-trend {
  position: relative;
  width: 60px;
  height: 60px;
}

.trend-line {
  position: absolute;
  height: 2px;
  background: var(--gradient-primary);
  transform-origin: left center;
}

.trend-line.line-1 {
  width: 30px;
  top: 40%;
  left: 5px;
  transform: rotate(-20deg);
}

.trend-line.line-2 {
  width: 25px;
  top: 50%;
  left: 20px;
  transform: rotate(15deg);
}

.trend-line.line-3 {
  width: 20px;
  top: 45%;
  right: 10px;
  transform: rotate(-10deg);
}

.trend-dot {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--accent-red);
}

.trend-dot.dot-1 {
  top: 28px;
  left: 8px;
}

.trend-dot.dot-2 {
  top: 18px;
  left: 30px;
  background: var(--accent-purple);
}

.trend-dot.dot-3 {
  top: 22px;
  right: 8px;
  background: var(--accent-cyan);
}

/* Knowledge Integration Icon */
.icon-knowledge {
  position: relative;
  width: 60px;
  height: 60px;
}

.knowledge-sources {
  position: relative;
  width: 100%;
  height: 100%;
}

.source-node {
  position: absolute;
  width: 16px;
  height: 16px;
  border-radius: 4px;
  border: 2px solid;
}

.source-node.doc {
  top: 8px;
  left: 8px;
  background: var(--accent-purple);
  border-color: var(--accent-purple);
}

.source-node.link {
  top: 8px;
  right: 8px;
  background: var(--accent-cyan);
  border-color: var(--accent-cyan);
  border-radius: 50%;
}

.source-node.feed {
  bottom: 8px;
  left: 8px;
  background: var(--accent-red);
  border-color: var(--accent-red);
  border-radius: 8px;
}

.knowledge-center {
  position: absolute;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--gradient-primary);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pulse-knowledge 2s ease-in-out infinite;
}

@keyframes pulse-knowledge {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    transform: translate(-50%, -50%) scale(1.1);
  }
}

.knowledge-flow {
  position: absolute;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--accent-red) 50%,
    transparent 100%
  );
  animation: flow 3s ease-in-out infinite;
}

.knowledge-flow.flow-1 {
  width: 25px;
  top: 15px;
  left: 20px;
  transform: rotate(45deg);
  animation-delay: 0s;
}

.knowledge-flow.flow-2 {
  width: 25px;
  top: 15px;
  right: 20px;
  transform: rotate(-45deg);
  animation-delay: 1s;
}

.knowledge-flow.flow-3 {
  width: 25px;
  bottom: 15px;
  left: 20px;
  transform: rotate(-45deg);
  animation-delay: 2s;
}

@keyframes flow {
  0%,
  100% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
}

/* Viral Icon */
.icon-viral {
  position: relative;
  width: 60px;
  height: 60px;
}

.viral-bubble {
  position: absolute;
  border-radius: 50%;
  border: 2px solid var(--accent-red);
}

.viral-bubble.bubble-1 {
  width: 20px;
  height: 20px;
  top: 20px;
  left: 20px;
}

.viral-bubble.bubble-2 {
  width: 15px;
  height: 15px;
  top: 10px;
  right: 15px;
  border-color: var(--accent-purple);
}

.viral-bubble.bubble-3 {
  width: 18px;
  height: 18px;
  bottom: 10px;
  left: 10px;
  border-color: var(--accent-cyan);
}

.viral-pulse {
  position: absolute;
  width: 40px;
  height: 40px;
  top: 10px;
  left: 10px;
  border-radius: 50%;
  background: var(--accent-red);
  opacity: 0.2;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(0.8);
    opacity: 0;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.3;
  }
}

/* Analytics Icon */
.icon-analytics {
  position: relative;
  width: 60px;
  height: 60px;
}

.chart-bar {
  position: absolute;
  bottom: 10px;
  width: 8px;
  background: var(--gradient-primary);
  border-radius: 4px 4px 0 0;
}

.chart-bar.bar-1 {
  left: 10px;
  height: 15px;
}

.chart-bar.bar-2 {
  left: 20px;
  height: 25px;
  background: var(--accent-purple);
}

.chart-bar.bar-3 {
  left: 30px;
  height: 20px;
  background: var(--accent-cyan);
}

.chart-bar.bar-4 {
  left: 40px;
  height: 30px;
  background: var(--accent-red);
}

.chart-line {
  position: absolute;
  width: 45px;
  height: 2px;
  background: var(--accent-red);
  top: 15px;
  left: 8px;
  transform: rotate(-15deg);
}

.chart-line::after {
  content: "";
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--accent-red);
  border-radius: 50%;
  right: -3px;
  top: -2px;
}

.feature-title {
  font-size: 24px;
  margin-bottom: 16px;
}

.feature-description {
  color: var(--text-secondary);
  margin-bottom: 20px;
  line-height: 1.6;
}

.feature-list {
  list-style: none;
}

.feature-list li {
  color: var(--text-secondary);
  font-size: 14px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.feature-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--accent-red);
  font-weight: bold;
}

/* Screenshots Section */
.screenshots {
  padding: 100px 0;
}

.screenshots-showcase {
  display: flex;
  flex-direction: column;
  gap: 100px;
}

.screenshot-item {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  opacity: 0;
  transform: translateX(-50px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 80px 0;
}

.screenshot-item.reverse {
  transform: translateX(50px);
}

.screenshot-item.reverse .screenshot-image {
  order: 2;
}

.screenshot-item.reverse .screenshot-info {
  order: 1;
}

.screenshot-item.animate-in {
  opacity: 1;
  transform: translateX(0);
}

.browser-mockup {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.browser-bar {
  background: var(--bg-tertiary);
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
}

.browser-dots {
  display: flex;
  gap: 8px;
}

.browser-dots span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: var(--text-muted);
  opacity: 0.5;
}

.screenshot-content {
  padding: 30px;
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.screenshot-image {
  /* aspect-ratio: 1 / 1; */
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.demo-personas {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
}

.demo-persona {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.demo-persona.active {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, 0.1);
}

.dp-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-primary);
  border-radius: 50%;
  border: 2px solid var(--border-color);
  color: var(--text-secondary);
  transition: all 0.3s ease;
  overflow: hidden;
}

.dp-icon svg {
  width: 40px;
  height: 40px;
  transition: transform 0.3s ease;
}

.demo-persona:hover .dp-icon svg {
  transform: scale(1.1);
}

.demo-persona.active .dp-icon {
  border-color: var(--accent-red);
  box-shadow: 0 0 0 2px rgba(255, 46, 77, 0.2);
  transform: scale(1.05);
}

.demo-persona.active .dp-icon svg {
  transform: scale(1.1);
}

/* Individual persona styling */
.dp-icon.lily {
  background: linear-gradient(135deg, #ffe4b5 0%, #f5deb3 100%);
}

.dp-icon.fiona {
  background: linear-gradient(135deg, #fdbcb4 0%, #ffb6c1 100%);
}

.dp-icon.fred {
  background: linear-gradient(135deg, #ddbea9 0%, #ddd6c1 100%);
}

.demo-persona h5 {
  font-size: 16px;
  margin-bottom: 4px;
}

.demo-persona p {
  font-size: 12px;
  color: var(--text-muted);
}

.demo-content-gen {
  width: 100%;
}

.gen-input {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.gen-input input {
  flex: 1;
  padding: 12px 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
}

.gen-input button {
  padding: 12px 24px;
  background: var(--gradient-primary);
  border: none;
  border-radius: 8px;
  color: white;
  font-weight: 600;
  cursor: pointer;
}

.gen-output {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.content-variant {
  padding: 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.content-variant.active {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, 0.05);
}

.content-variant p {
  font-size: 14px;
  margin-bottom: 12px;
  line-height: 1.4;
}

.variant-platforms {
  display: flex;
  gap: 8px;
}

.variant-platforms span {
  font-size: 16px;
  opacity: 0.7;
}

.demo-camera-roll {
  width: 100%;
}

.photo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.photo-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.photo-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    var(--bg-primary) 25%,
    var(--bg-tertiary) 25%,
    var(--bg-tertiary) 50%,
    var(--bg-primary) 50%,
    var(--bg-primary) 75%,
    var(--bg-tertiary) 75%
  );
  background-size: 20px 20px;
}

.photo-tag {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
}

.used-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: var(--accent-red);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 600;
}

.photo-item.tagged {
  border-color: var(--accent-cyan);
}

.photo-item.used {
  opacity: 0.6;
}

.photo-item.selected {
  border-color: var(--accent-red);
  transform: scale(1.05);
}

.photo-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 46, 77, 0.9);
  color: white;
  padding: 8px;
  font-size: 11px;
  text-align: center;
}

.trend-suggestions {
  margin-top: 20px;
  padding: 16px;
  background: var(--bg-primary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.trend-suggestions h4 {
  font-size: 14px;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.trend-chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.trend-chip {
  padding: 6px 12px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  font-size: 12px;
  color: var(--text-secondary);
}

.trend-chip.hot {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, 0.1);
  color: var(--accent-red);
}

/* Persona Manager Demo - Compact Version */
.demo-persona-manager-compact {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 32px;
  transform: scale(1);
  transform-origin: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.persona-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.persona-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.persona-card:hover {
  border-color: var(--accent-red);
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(255, 46, 77, 0.2);
}

.persona-card.active {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, 0.08);
  box-shadow: 0 0 0 1px rgba(255, 46, 77, 0.1);
}

.persona-card-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 24px;
  color: white;
}

.persona-card-avatar.fiona {
  background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
}

.persona-card-avatar.marcus {
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
}

.persona-card h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.persona-card p {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.persona-knowledge-preview {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: center;
}

.knowledge-chip-mini {
  padding: 4px 8px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  font-size: 10px;
  color: var(--text-secondary);
}

.knowledge-chip-mini.active {
  background: rgba(255, 46, 77, 0.1);
  border-color: var(--accent-red);
  color: var(--accent-red);
}

/* Content Generation Demo - Compact Version */
.demo-content-generation-compact {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  transform-origin: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* AI Generation Showcase */
.ai-generation-showcase {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 100%;
}

.generation-input-section,
.generation-output-section {
  background: var(--bg-tertiary);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid var(--border-color);
}

.input-header,
.output-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.input-label,
.output-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
}

.ai-status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--text-secondary);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--text-muted);
}

.status-dot.active {
  background: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
}

.input-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.input-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  text-align: center;
}

.card-icon {
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 32px;
}

.card-label {
  font-size: 11px;
  color: var(--text-secondary);
  display: block;
  margin-bottom: 8px;
}

.photo-thumbnail {
  width: 100%;
  height: 40px;
  border-radius: 4px;
}

.trend-tags {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.trend-tag {
  font-size: 10px;
  background: rgba(124, 58, 237, 0.1);
  color: var(--accent-purple);
  padding: 2px 6px;
  border-radius: 4px;
}

.persona-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
}

/* AI Processing Divider */
.ai-processing-divider {
  position: relative;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.processing-line {
  position: absolute;
  width: 100%;
  height: 2px;
  background: var(--border-color);
  overflow: hidden;
}

.processing-pulse {
  position: absolute;
  width: 50%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    var(--accent-red),
    transparent
  );
  animation: processingPulse 2s ease-in-out infinite;
}

@keyframes processingPulse {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(200%);
  }
}

.ai-badge {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  padding: 6px 16px;
  z-index: 1;
}

.ai-badge span {
  font-size: 12px;
  font-weight: 600;
  background: var(--gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Output Section */
.platform-indicator {
  font-size: 12px;
  color: var(--text-secondary);
}

.mini-post {
  background: var(--bg-secondary);
  border-radius: 8px;
  padding: 16px;
  border: 1px solid var(--border-color);
}

.mini-post-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.mini-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.mini-username {
  font-size: 13px;
  font-weight: 500;
  color: var(--text-primary);
}

.mini-post-text {
  font-size: 13px;
  line-height: 1.5;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.mini-post-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mini-post-meta span {
  font-size: 11px;
  color: var(--text-secondary);
}

.mini-post-button {
  background: var(--accent-red);
  color: white;
  border: none;
  border-radius: 6px;
  padding: 4px 12px;
  font-size: 11px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mini-post-button:hover {
  background: var(--accent-purple);
  transform: translateY(-1px);
}

.generation-process {
  margin-bottom: 24px;
}

.process-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 20px 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  min-width: 120px;
  transition: all 0.3s ease;
  position: relative;
}

.step-item.active {
  border-color: var(--accent-red);
  background: rgba(255, 46, 77, 0.08);
  box-shadow: 0 4px 16px rgba(255, 46, 77, 0.15);
}

.step-icon {
  font-size: 28px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border-radius: 50%;
  transition: all 0.3s ease;
}

.step-item.active .step-icon {
  background: rgba(255, 46, 77, 0.15);
  box-shadow: 0 0 0 2px rgba(255, 46, 77, 0.2);
}

.step-item span {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-secondary);
  text-align: center;
}

.step-item.active span {
  color: var(--text-primary);
}

.step-arrow {
  font-size: 20px;
  color: var(--accent-red);
  font-weight: bold;
}

.generated-content-preview {
  background: var(--bg-primary);
  border-radius: 12px;
  padding: 20px;
}

.content-example {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.content-header {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.persona-badge,
.trend-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.persona-badge {
  background: rgba(255, 46, 77, 0.1);
  color: var(--accent-red);
  border: 1px solid var(--accent-red);
}

.trend-badge {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.content-body p {
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.content-platforms {
  display: flex;
  gap: 8px;
}

.platform-icon {
  font-size: 16px;
  opacity: 0.7;
}

/* Original Full Manager Demo */
.demo-persona-manager {
  display: flex;
  height: 400px;
  border: 1px solid var(--border-color);
  border-radius: 12px;
  overflow: hidden;
  transform: scale(0.85);
  transform-origin: center;
  margin: 20px 0;
}

.persona-sidebar {
  width: 300px;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  overflow-y: auto;
}

.persona-list-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  background: var(--bg-secondary);
  transition: all 0.3s ease;
}

.persona-list-item:hover {
  background: var(--bg-tertiary);
}

.persona-list-item.active {
  background: rgba(255, 46, 77, 0.1);
  border-right: 3px solid var(--accent-red);
}

.persona-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  color: white;
}

.persona-avatar.fiona {
  background: linear-gradient(135deg, #ff69b4 0%, #ff1493 100%);
}

.persona-avatar.marcus {
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
}

.persona-info h4 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.persona-info p {
  font-size: 12px;
  color: var(--text-secondary);
}

.persona-detail-form {
  flex: 1;
  padding: 32px;
  background: var(--bg-primary);
  overflow-y: auto;
}

.form-group {
  margin-bottom: 24px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--accent-red);
}

.form-group textarea {
  min-height: 80px;
  resize: vertical;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.knowledge-sources {
  margin-top: 16px;
}

.knowledge-chips {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 12px;
}

.knowledge-chip {
  padding: 6px 12px;
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  font-size: 12px;
  color: var(--text-secondary);
}

.knowledge-chip.active {
  background: rgba(255, 46, 77, 0.1);
  border-color: var(--accent-red);
  color: var(--accent-red);
}

.persona-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
}

.persona-sources {
  margin-bottom: 20px;
}

.persona-sources h6 {
  font-size: 12px;
  color: var(--text-muted);
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.source-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.source-item {
  padding: 4px 12px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  font-size: 12px;
  color: var(--text-secondary);
}

.persona-style {
  padding: 16px;
  background: var(--bg-primary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.persona-style p {
  font-size: 13px;
  color: var(--text-secondary);
  font-style: italic;
}

/* AI Process Demo */
.demo-ai-process {
  padding: 30px 20px;
}

.process-step {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.process-step.active {
  opacity: 1;
}

.step-icon {
  font-size: 24px;
}

.process-step span:last-child {
  font-size: 12px;
  color: var(--text-secondary);
}

.process-arrow {
  display: inline-block;
  margin: 0 16px;
  color: var(--text-muted);
  font-size: 20px;
}

.generated-content {
  margin-top: 30px;
}

.content-card {
  display: flex;
  gap: 16px;
  padding: 16px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
}

.content-card img {
  width: 60px;
  height: 60px;
  border-radius: 6px;
  object-fit: cover;
}

.content-text {
  flex: 1;
}

.content-text p {
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.content-meta {
  font-size: 11px;
  color: var(--text-muted);
}

.feature-card.primary {
  background: linear-gradient(
    135deg,
    rgba(255, 46, 77, 0.1) 0%,
    rgba(124, 58, 237, 0.1) 100%
  );
  border-color: rgba(255, 46, 77, 0.3);
}

.screenshot-info h3 {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 20px;
  color: var(--text-primary);
  line-height: 1.2;
}

.screenshot-info p {
  font-size: 18px;
  color: var(--text-secondary);
  line-height: 1.7;
  max-width: 480px;
}

/* CTA Section */
.cta {
  padding: 100px 0;
  background: var(--bg-secondary);
  text-align: center;
}

.cta-title {
  font-size: 40px;
  margin-bottom: 16px;
}

.cta-subtitle {
  font-size: 18px;
  color: var(--text-secondary);
  margin-bottom: 40px;
}

.cta-form {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 24px;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.cta-input {
  flex: 1;
  padding: 16px 20px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 16px;
}

.cta-input:focus {
  outline: none;
  border-color: var(--accent-red);
}

.cta-note {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Footer */
.footer {
  padding: 60px 0 30px;
  border-top: 1px solid var(--border-color);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.footer-brand {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.footer-tagline {
  color: var(--text-muted);
  font-size: 14px;
}

.footer-links {
  display: flex;
  gap: 32px;
}

.footer-links a {
  color: var(--text-secondary);
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: var(--accent-red);
}

.footer-bottom {
  text-align: center;
  padding-top: 30px;
  border-top: 1px solid var(--border-color);
  color: var(--text-muted);
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero .container {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .hero-title {
    max-width: 100%;
  }

  .hero-visual {
    height: 400px;
  }

  /* 优化浮动卡片在平板设备上的显示 */
  .floating-card {
    padding: 16px;
    border-radius: 12px;
  }

  .floating-card.card-1 {
    top: 5%;
    left: 2%;
    transform: rotate(-3deg);
  }

  .floating-card.card-2 {
    top: 20%;
    right: 5%;
    transform: rotate(2deg);
  }

  .social-post-preview {
    width: 240px;
  }

  .post-image {
    height: 140px;
  }

  .screenshot-item {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .screenshot-item.reverse .screenshot-image {
    order: 0;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 16px;
  }

  /* Navbar mobile adjustments */
  .navbar {
    padding: 12px 0;
  }

  .nav-logo .logo-text {
    font-size: 18px;
  }

  .nav-cta .btn-primary {
    padding: 10px 16px;
    font-size: 12px;
  }

  .hero {
    padding: 100px 0 60px;
  }

  .hero-badge {
    padding: 8px 20px;
    font-size: 12px;
    margin-bottom: 24px;
  }

  .hero-title {
    font-size: 32px;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 16px;
    line-height: 1.5;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    gap: 20px;
  }

  /* 移动端浮动卡片优化 - 保证宽度并允许重叠 */
  .hero-visual {
    height: 400px;
    margin-top: 40px;
    position: relative;
  }

  .floating-card {
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  .floating-card.card-1 {
    top: 10%;
    left: 5%;
    transform: rotate(-3deg) scale(0.9);
    z-index: 2;
  }

  .floating-card.card-2 {
    top: 30%;
    right: 10%;
    transform: rotate(2deg) scale(0.9);
    z-index: 1;
  }

  .social-post-preview {
    width: 260px;
  }

  .post-header {
    margin-bottom: 8px;
  }

  .post-avatar {
    width: 32px;
    height: 32px;
  }

  .post-username {
    font-size: 12px;
  }

  .post-time {
    font-size: 10px;
  }

  .post-text {
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 8px;
  }

  .post-image {
    height: 140px;
  }

  .post-engagement {
    gap: 12px;
    font-size: 11px;
  }

  .ai-pulse {
    width: 10px;
    height: 10px;
    top: -1px;
    right: -1px;
  }

  /* 移动端重叠效果优化 */
  .hero-mockup {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: visible; /* 允许卡片稍微超出边界以实现重叠 */
  }

  /* 确保卡片有足够的重叠空间 */
  .floating-card.card-1 {
    width: auto;
    max-width: none;
  }

  .floating-card.card-2 {
    width: auto;
    max-width: none;
  }

  .section-title {
    font-size: 32px;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .screenshot-info h3 {
    font-size: 24px;
  }

  .screenshot-info p {
    font-size: 16px;
  }

  .cta-form {
    flex-direction: column;
  }

  .footer-content {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }

  .footer-links {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .hero-title {
    font-size: 26px;
    line-height: 1.3;
  }

  .hero-subtitle {
    font-size: 14px;
  }

  .hero-badge {
    padding: 6px 16px;
    font-size: 11px;
  }

  .btn-large {
    padding: 14px 24px;
    font-size: 14px;
  }

  /* 小屏幕设备浮动卡片进一步优化 - 保持重叠效果 */
  .hero-visual {
    height: 350px;
    margin-top: 30px;
  }

  .floating-card {
    padding: 14px;
    border-radius: 10px;
  }

  .floating-card.card-1 {
    top: 8%;
    left: 2%;
    transform: rotate(-2deg) scale(0.85);
  }

  .floating-card.card-2 {
    top: 35%;
    right: 5%;
    transform: rotate(1deg) scale(0.85);
  }

  .social-post-preview {
    width: 220px;
  }

  .post-header {
    gap: 8px;
    margin-bottom: 6px;
  }

  .post-avatar {
    width: 28px;
    height: 28px;
  }

  .post-username {
    font-size: 11px;
  }

  .post-time {
    font-size: 9px;
  }

  .post-text {
    font-size: 11px;
    line-height: 1.3;
    margin-bottom: 6px;
  }

  .post-image {
    height: 100px;
  }

  .post-engagement {
    gap: 8px;
    font-size: 10px;
  }

  .ai-pulse {
    width: 8px;
    height: 8px;
  }

  /* 小屏幕重叠布局优化 - 允许重叠以实现更好的视觉效果 */
  .hero-mockup {
    overflow: visible;
    position: relative;
  }

  /* 调整卡片位置以实现更好的重叠效果 */
  .floating-card.card-1 {
    position: absolute;
    z-index: 3;
  }

  .floating-card.card-2 {
    position: absolute;
    z-index: 2;
  }

  .section-title {
    font-size: 28px;
  }

  .feature-card {
    padding: 30px;
  }

  .cta-title {
    font-size: 32px;
  }
}

/* 中等屏幕设备重叠优化 */
@media (max-width: 640px) and (min-width: 481px) {
  .floating-card.card-1 {
    top: 12%;
    left: 0%;
    transform: rotate(-2deg) scale(0.88);
  }

  .floating-card.card-2 {
    top: 32%;
    right: 3%;
    transform: rotate(1deg) scale(0.88);
  }

  .social-post-preview {
    width: 240px;
  }

  .hero-visual {
    height: 380px;
  }
}

/* 移动端动画优化 */
@media (max-width: 768px) {
  /* 减少移动端动画强度以提升性能 */
  .floating-card {
    animation-duration: 8s;
  }

  /* 移动端浮动动画保持基础变换 */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0);
    }
    50% {
      transform: translateY(-8px);
    }
  }

  /* 确保卡片1的浮动动画包含旋转 */
  .floating-card.card-1 {
    animation: float 8s ease-in-out infinite;
  }

  .floating-card.card-1:hover {
    transform: rotate(-2deg) scale(0.9) translateY(-5px);
  }

  /* 确保卡片2的浮动动画包含旋转 */
  .floating-card.card-2 {
    animation: float 8s ease-in-out infinite 3s;
  }

  .floating-card.card-2:hover {
    transform: rotate(1deg) scale(0.9) translateY(-5px);
  }

  /* 移动端禁用一些复杂动画效果 */
  .ai-glow {
    opacity: 0.3;
  }

  .ai-shimmer {
    opacity: 0.5;
  }
}

/* 超小屏幕设备进一步优化 - 保持协调的重叠布局 */
@media (max-width: 360px) {
  .hero-visual {
    height: 350px;
  }

  .floating-card {
    padding: 12px;
  }

  /* 调整卡片位置实现更好的重叠效果 */
  .floating-card.card-1 {
    top: 8%;
    left: -5%;
    transform: rotate(-2deg) scale(0.82);
    z-index: 3;
  }

  .floating-card.card-2 {
    top: 35%;
    right: -8%;
    transform: rotate(1deg) scale(0.82);
    z-index: 2;
  }

  .social-post-preview {
    width: 210px;
  }

  .post-image {
    height: 110px;
  }

  /* 确保重叠区域的视觉层次 */
  .floating-card.card-1:hover {
    z-index: 4;
    transform: rotate(-2deg) scale(0.85) translateY(-3px);
  }

  .floating-card.card-2:hover {
    z-index: 3;
    transform: rotate(1deg) scale(0.85) translateY(-3px);
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus states for accessibility */
button:focus,
input:focus,
a:focus {
  outline: 2px solid var(--accent-red);
  outline-offset: 2px;
}

/* Real Camera Roll Design Styles - Optimized for Landing Page */
.camera-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
  border-radius: 8px 8px 0 0;
}

.camera-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 18px;
}

.camera-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.camera-tip {
  font-size: 13px;
  color: var(--text-muted);
}

.camera-filter-tabs {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 8px 14px;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 13px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.filter-tab.active {
  background: var(--accent-red);
  color: white;
  border-color: var(--accent-red);
  box-shadow: 0 2px 8px rgba(255, 46, 77, 0.3);
}

.filter-tab:hover:not(.active) {
  border-color: var(--border-hover);
  background: var(--bg-tertiary);
}

.real-photo-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 24px;
  background: var(--bg-primary);
  border-radius: 0 0 8px 8px;
}

.real-photo-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  transition: all 0.3s ease;
  cursor: pointer;
  max-width: 100%;
  position: relative;
}

.real-photo-card:hover {
  background: var(--bg-tertiary);
  box-shadow: 0 8px 24px rgba(6, 182, 212, 0.2);
  transform: translateY(-4px);
  border-color: var(--accent-cyan);
}

.real-photo-card.selected {
  border-color: var(--accent-red);
  box-shadow: 0 8px 24px rgba(255, 46, 77, 0.25);
  transform: scale(1.02) translateY(-2px);
}

.photo-image-container {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.photo-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #333 0%, #555 100%);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-placeholder.fashion {
  background: linear-gradient(135deg, #ff6b7a 0%, #c44569 100%);
}

.photo-placeholder.lifestyle {
  background: linear-gradient(135deg, #4834d4 0%, #686de0 100%);
}

.photo-placeholder.food {
  background: linear-gradient(135deg, #ff9ff3 0%, #f368e0 100%);
}

/* Add image icons to placeholders */
.photo-placeholder::before {
  content: "📷";
  font-size: 24px;
  opacity: 0.7;
}

.photo-placeholder.fashion::before {
  content: "👗";
}

.photo-placeholder.lifestyle::before {
  content: "☕";
}

.photo-placeholder.food::before {
  content: "🍝";
}

.delete-button {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 28px;
  height: 28px;
  background: rgba(239, 68, 68, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(4px);
}

.real-photo-card:hover .delete-button {
  opacity: 1;
}

.photo-info {
  padding: 16px;
}

.photo-filename {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.photo-description {
  font-size: 12px;
  color: var(--text-muted);
  line-height: 1.5;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.photo-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.photo-date {
  font-size: 11px;
  color: var(--text-muted);
}

.photo-status {
  font-size: 11px;
  padding: 3px 8px;
  border-radius: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.photo-status.used {
  background: rgba(255, 46, 77, 0.15);
  color: var(--accent-red);
}

.photo-status.unused {
  background: var(--bg-tertiary);
  color: var(--text-muted);
}

.photo-tags {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  margin-bottom: 12px;
}

.photo-tags .tag {
  font-size: 10px;
  padding: 4px 8px;
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: 6px;
  border: 1px solid var(--border-color);
  font-weight: 500;
}

.photo-type-badge {
  display: inline-block;
  font-size: 11px;
  padding: 4px 10px;
  background: var(--accent-red);
  color: white;
  border-radius: 6px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Landing page specific adjustments */
.demo-camera-roll {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  transition: all 0.4s ease;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.demo-camera-roll:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 60px rgba(255, 46, 77, 0.15);
  border-color: rgba(255, 46, 77, 0.3);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}
