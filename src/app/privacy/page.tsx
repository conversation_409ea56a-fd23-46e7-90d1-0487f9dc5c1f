"use client";

import { useTranslations } from "next-intl";
import Navigation from "@/components/Navigation";
import FooterSection from "@/components/sections/FooterSection";

export default function PrivacyPage() {
  const t = useTranslations("Privacy");

  return (
    <div className="min-h-screen">
      <Navigation />

      {/* Privacy Policy Content */}
      <section className="legal-page mt-28">
        <div className="container">
          <div className="legal-content">
            <h1 className="gradient-text text-3xl mb-1">{t("title")}</h1>
            <p className="last-updated">{t("lastUpdated")}</p>

            <p>{t("introduction")}</p>

            <h2>{t("section1.title")}</h2>
            <p>{t("section1.description")}</p>
            <ul>
              <li>{t("section1.items.account")}</li>
              <li>{t("section1.items.photos")}</li>
              <li>{t("section1.items.personas")}</li>
              <li>{t("section1.items.knowledge")}</li>
              <li>{t("section1.items.social")}</li>
              <li>{t("section1.items.content")}</li>
            </ul>

            <h2>{t("section2.title")}</h2>
            <p>{t("section2.description")}</p>
            <ul>
              <li>{t("section2.items.provide")}</li>
              <li>{t("section2.items.manage")}</li>
              <li>{t("section2.items.generate")}</li>
              <li>{t("section2.items.post")}</li>
              <li>{t("section2.items.improve")}</li>
              <li>{t("section2.items.communicate")}</li>
            </ul>

            <h2>{t("section3.title")}</h2>
            <p>{t("section3.description")}</p>

            <h2>{t("section4.title")}</h2>
            <p>{t("section4.description")}</p>

            <h2>{t("section5.title")}</h2>
            <p>{t("section5.description")}</p>
            <ul>
              <li>{t("section5.items.google")}</li>
              <li>{t("section5.items.twitter")}</li>
              <li>{t("section5.items.cloud")}</li>
            </ul>
            <p>{t("section5.note")}</p>

            <h2>{t("section6.title")}</h2>
            <p>{t("section6.description")}</p>

            <h2>{t("section7.title")}</h2>
            <p>{t("section7.description")}</p>
            <ul>
              <li>{t("section7.items.access")}</li>
              <li>{t("section7.items.correct")}</li>
              <li>{t("section7.items.delete")}</li>
              <li>{t("section7.items.export")}</li>
              <li>{t("section7.items.optout")}</li>
            </ul>

            <h2>{t("section8.title")}</h2>
            <p>{t("section8.description")}</p>

            <h2>{t("section9.title")}</h2>
            <p>{t("section9.description")}</p>

            <h2>{t("section10.title")}</h2>
            <p>{t("section10.description")}</p>
          </div>
        </div>
      </section>

      <FooterSection />
      <style jsx>
        {`
          .legal-page {
            min-height: 100vh;
          }
          .legal-content {
            max-width: 800px;
            margin: 0 auto;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 16px;
            padding: 48px;
          }
          .legal-content h1 {
            font-size: 36px;
            margin-bottom: 16px;
            background: var(--gradient-primary);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .legal-content .last-updated {
            color: var(--text-secondary);
            font-size: 14px;
            margin-bottom: 40px;
          }
          .legal-content h2 {
            font-size: 24px;
            margin-top: 32px;
            margin-bottom: 16px;
            color: var(--text-primary);
          }
          .legal-content p,
          .legal-content li {
            line-height: 1.8;
            color: var(--text-secondary);
            margin-bottom: 16px;
          }
          .legal-content ul {
            margin-left: 24px;
            margin-bottom: 16px;
          }
          .legal-content a {
            color: var(--accent-red);
            text-decoration: none;
            transition: color 0.2s ease;
          }
          .legal-content a:hover {
            color: var(--accent-purple);
          }
          @media (max-width: 768px) {
            .legal-content {
              padding: 32px 24px;
            }
            .legal-content h1 {
              font-size: 28px;
            }
          }
        `}
      </style>
    </div>
  );
}
