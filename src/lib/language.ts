"use client";

export type Locale = "en" | "zh" | "ja";

export const locales: Locale[] = ["en", "zh", "ja"];
export const defaultLocale: Locale = "en";

export const languageNames = {
  en: "English",
  zh: "中文",
  ja: "日本語",
} as const;

export const languageFlags = {
  en: "🇺🇸",
  zh: "🇨🇳",
  ja: "🇯🇵",
} as const;

// 语言存储键
const LANGUAGE_STORAGE_KEY = "personaroll-language";

// 获取当前语言
export function getCurrentLanguage(): Locale {
  if (typeof window === "undefined") {
    return defaultLocale;
  }

  // 1. 优先从 localStorage 获取
  const stored = localStorage.getItem(LANGUAGE_STORAGE_KEY);
  if (stored && locales.includes(stored as Locale)) {
    return stored as Locale;
  }

  // 2. 从浏览器语言检测
  const browserLang = navigator.language.split("-")[0];
  if (locales.includes(browserLang as Locale)) {
    return browserLang as Locale;
  }

  // 3. 返回默认语言
  return defaultLocale;
}

// 设置语言
export function setLanguage(locale: Locale): void {
  if (typeof window === "undefined") return;

  if (locales.includes(locale)) {
    localStorage.setItem(LANGUAGE_STORAGE_KEY, locale);
    // 触发自定义事件通知语言变更
    window.dispatchEvent(new CustomEvent("languageChange", { detail: locale }));
  }
}

// 获取语言消息
export async function getMessages(locale: Locale) {
  try {
    const messages = await import(`../../messages/${locale}.json`);
    return messages.default;
  } catch (error) {
    console.warn(`Failed to load messages for locale: ${locale}`, error);
    // 回退到默认语言
    const defaultMessages = await import(
      `../../messages/${defaultLocale}.json`
    );
    return defaultMessages.default;
  }
}
