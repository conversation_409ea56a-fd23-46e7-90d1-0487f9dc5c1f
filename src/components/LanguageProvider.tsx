/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { NextIntlClientProvider } from "next-intl";
import {
  Locale,
  getCurrentLanguage,
  setLanguage as setStoredLanguage,
  getMessages,
  defaultLocale,
} from "@/lib/language";

interface LanguageContextType {
  locale: Locale;
  setLanguage: (locale: Locale) => void;
  isLoading: boolean;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined
);

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}

interface LanguageProviderProps {
  children: React.ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [locale, setLocale] = useState<Locale>(defaultLocale);
  const [messages, setMessages] = useState<Record<string, any> | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // 初始化语言
  useEffect(() => {
    const initLanguage = async () => {
      const currentLang = getCurrentLanguage();
      const msgs = await getMessages(currentLang);

      setLocale(currentLang);
      setMessages(msgs);
      setIsLoading(false);
    };

    initLanguage();
  }, []);

  // 监听语言变更事件
  useEffect(() => {
    const handleLanguageChange = async (event: Event) => {
      const customEvent = event as CustomEvent<Locale>;
      const newLocale = customEvent.detail;
      setIsLoading(true);

      try {
        const msgs = await getMessages(newLocale);
        setLocale(newLocale);
        setMessages(msgs);
      } catch (error) {
        console.error("Failed to change language:", error);
      } finally {
        setIsLoading(false);
      }
    };

    window.addEventListener("languageChange", handleLanguageChange);

    return () => {
      window.removeEventListener("languageChange", handleLanguageChange);
    };
  }, []);

  const setLanguage = (newLocale: Locale) => {
    setStoredLanguage(newLocale);
  };

  const contextValue: LanguageContextType = {
    locale,
    setLanguage,
    isLoading,
  };

  // 在消息加载完成前显示加载状态
  if (isLoading || !messages) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <LanguageContext.Provider value={contextValue}>
      <NextIntlClientProvider locale={locale} messages={messages}>
        {children}
      </NextIntlClientProvider>
    </LanguageContext.Provider>
  );
}
