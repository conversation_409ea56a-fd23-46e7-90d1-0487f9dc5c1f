"use client";

import { motion, useScroll, useTransform } from "framer-motion";
import { useTranslations } from "next-intl";
import { useState, useEffect } from "react";
// import LanguageSwitcher from "./LanguageSwitcher";

export default function Navigation() {
  const tCommon = useTranslations("Common");
  const tNavigation = useTranslations("Navigation");
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const { scrollY } = useScroll();

  // 导航栏背景透明度动画
  const navOpacity = useTransform(scrollY, [0, 100], [0, 0.95]);
  const navBlur = useTransform(scrollY, [0, 100], [0, 10]);

  useEffect(() => {
    // 延迟设置mounted状态，确保DOM完全准备好
    const timer = setTimeout(() => {
      setIsMounted(true);
    }, 50);

    const unsubscribe = scrollY.on("change", (latest) => {
      setIsScrolled(latest > 50);
    });

    return () => {
      clearTimeout(timer);
      unsubscribe();
    };
  }, [scrollY]);

  // Logo动画变体
  const logoVariants = {
    initial: { opacity: 0, x: -20, scale: 0.9 },
    animate: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.1, 0.35, 1] as const,
      },
    },
    hover: {
      scale: 1.05,
      transition: { duration: 0.2 },
    },
  };

  // 按钮动画变体
  const buttonVariants = {
    initial: { opacity: 0, x: 20, scale: 0.9 },
    animate: {
      opacity: 1,
      x: 0,
      scale: 1,
      transition: {
        duration: 0.8,
        delay: 0.2,
        ease: [0.25, 0.1, 0.35, 1] as const,
      },
    },
    hover: {
      scale: 1.05,
      y: -2,
      transition: { duration: 0.2 },
    },
    tap: {
      scale: 0.95,
      transition: { duration: 0.1 },
    },
  };

  // Logo层动画
  const layerVariants = {
    animate: {
      rotate: [0, 360],
      transition: {
        duration: 20,
        repeat: Infinity,
        ease: "linear" as const,
      },
    },
  };

  // 防止初始闪动，等待组件挂载完成
  if (!isMounted) {
    return null;
  }

  return (
    <motion.nav
      className="navbar fixed top-0 left-0 right-0 z-40"
      style={{
        backgroundColor: `rgba(13, 13, 13, ${navOpacity.get() || 0})`,
        backdropFilter: `blur(${navBlur.get() || 0}px)`,
      }}
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.8, ease: [0.25, 0.1, 0.35, 1] as const }}
    >
      <div className="container">
        <motion.div
          className="nav-logo"
          variants={logoVariants}
          initial="initial"
          animate="animate"
          whileHover="hover"
        >
          <motion.div
            className="logo-icon"
            whileHover={{ rotate: 180 }}
            transition={{ duration: 0.6, ease: "easeInOut" }}
          >
            <div className="persona-layers">
              <motion.div
                className="persona-layer layer-1"
                variants={layerVariants}
                animate="animate"
              />
              <motion.div
                className="persona-layer layer-2"
                variants={layerVariants}
                animate="animate"
                style={{ animationDelay: "0.5s" }}
              />
              <motion.div
                className="persona-layer layer-3"
                variants={layerVariants}
                animate="animate"
                style={{ animationDelay: "1s" }}
              />
            </div>
          </motion.div>
          <motion.span
            className="logo-text"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            {tCommon("brandName")}
          </motion.span>
        </motion.div>

        <motion.div
          className="nav-cta"
          variants={buttonVariants}
          initial="initial"
          animate="animate"
          whileHover="hover"
          whileTap="tap"
        >
          {/* <LanguageSwitcher /> */}
          <motion.button
            className="btn-primary relative overflow-hidden"
            whileHover={{
              boxShadow: "0 10px 30px rgba(255, 46, 77, 0.3)",
            }}
          >
            <motion.span
              className="relative z-10"
              initial={{ y: 0 }}
              whileHover={{ y: -2 }}
              transition={{ duration: 0.2 }}
            >
              {tNavigation("login")}
            </motion.span>

            {/* 按钮光效 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-red-500/20 to-purple-600/20"
              initial={{ x: "-100%" }}
              whileHover={{ x: "100%" }}
              transition={{ duration: 0.6 }}
            />
          </motion.button>
        </motion.div>
      </div>

      {/* 导航栏底部光效 */}
      <motion.div
        className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-red-500/50 to-transparent"
        initial={{ scaleX: 0 }}
        animate={{ scaleX: isScrolled ? 1 : 0 }}
        transition={{ duration: 0.3 }}
      />
    </motion.nav>
  );
}
