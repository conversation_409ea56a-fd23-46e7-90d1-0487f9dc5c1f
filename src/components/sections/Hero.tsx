"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";

export default function Hero() {
  const tHero = useTranslations("Hero");
  const tCommon = useTranslations("Common");
  const tPersonas = useTranslations("Personas");
  const tContent = useTranslations("ContentGeneration");
  return (
    <section className="hero grid max-sm:grid-cols-1 grid-cols-2">
      <div className="hero-content">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="hero-badge">{tHero("badge")}</div>
          <h1 className="hero-title !text-5xl">
            {tHero("title")}
            <br />
            <span className="gradient-text mt-4 inline-block">
              {tHero("subtitle")}
            </span>
          </h1>
          <p className="hero-subtitle mt-4">{tHero("description")}</p>
          <div className="hero-actions">
            <button className="btn-primary btn-large">
              {tCommon("getEarlyAccess")}
            </button>
            <button className="btn-secondary btn-large">
              {tCommon("watchDemo")}
            </button>
          </div>
          <div className="hero-stats">
            <div className="stat">
              <span className="stat-number">{tHero("stats.ai")}</span>
              <span className="stat-label">{tHero("stats.aiLabel")}</span>
            </div>
            <div className="stat">
              <span className="stat-number">{tHero("stats.live")}</span>
              <span className="stat-label">{tHero("stats.liveLabel")}</span>
            </div>
            <div className="stat">
              <span className="stat-number">{tHero("stats.infinite")}</span>
              <span className="stat-label">{tHero("stats.infiniteLabel")}</span>
            </div>
          </div>
        </motion.div>
      </div>
      <div className="hero-visual">
        <div className="hero-mockup">
          <div className="floating-card card-1 ai-generated">
            <div className="ai-glow"></div>
            <div className="social-post-preview">
              <div className="post-header">
                <div className="post-avatar">
                  <div className="ai-pulse"></div>
                </div>
                <div className="post-meta">
                  <span className="post-username">@stylevibes</span>
                  <span className="post-time">
                    2m ago • <span className="ai-subtle">AI</span>
                  </span>
                </div>
              </div>
              <div className="post-image-container">
                <img
                  className="post-image"
                  src="https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=300&fit=crop"
                  alt="Aesthetic coffee shop with matcha latte"
                />
              </div>
              <p className="post-text">
                Found the perfect coffee spot with the most aesthetic vibes!
                ☕✨ This matcha latte is almost too pretty to drink!
              </p>
              <div className="post-engagement">
                <span>❤️ 234</span>
                <span>💬 45</span>
                <span>🔄 12</span>
              </div>
            </div>
            <div className="ai-shimmer"></div>
          </div>
          <div className="floating-card card-2 ai-generated">
            <div className="ai-glow"></div>
            <div className="social-post-preview">
              <div className="post-header">
                <div className="post-avatar foodie">
                  <div className="ai-pulse"></div>
                </div>
                <div className="post-meta">
                  <span className="post-username">@foodiedelights</span>
                  <span className="post-time">
                    15m ago • <span className="ai-subtle">AI</span>
                  </span>
                </div>
              </div>
              <div className="post-image-container">
                <img
                  className="post-image"
                  src="https://images.unsplash.com/photo-1565299624946-b28f40a0ae38?w=400&h=300&fit=crop"
                  alt="Brunch with pancakes and berries"
                />
              </div>
              <p className="post-text">
                Weekend brunch perfection! 🥐🍳 These fluffy pancakes with fresh
                berries are giving me all the feels!
              </p>
              <div className="post-engagement">
                <span>❤️ 567</span>
                <span>💬 89</span>
                <span>🔄 34</span>
              </div>
            </div>
            <div className="ai-shimmer"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
