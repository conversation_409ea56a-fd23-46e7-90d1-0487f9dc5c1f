"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";

export default function Hero() {
  const tHero = useTranslations("Hero");
  const tCommon = useTranslations("Common");
  return (
    <section className="hero grid max-sm:grid-cols-1 grid-cols-2">
      <div className="hero-content">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
        >
          <div className="hero-badge">{tHero("badge")}</div>
          <h1 className="hero-title !text-5xl">
            {tHero("title")}
            <br />
            <span className="gradient-text mt-4 inline-block">
              {tHero("subtitle")}
            </span>
          </h1>
          <p className="hero-subtitle mt-4">{tHero("description")}</p>
          <div className="hero-actions">
            <button className="btn-primary btn-large">
              {tCommon("getEarlyAccess")}
            </button>
            <button className="btn-secondary btn-large">
              {tCommon("watchDemo")}
            </button>
          </div>
          <div className="hero-stats">
            <div className="stat">
              <span className="stat-number">{tHero("stats.ai")}</span>
              <span className="stat-label">{tHero("stats.aiLabel")}</span>
            </div>
            <div className="stat">
              <span className="stat-number">{tHero("stats.live")}</span>
              <span className="stat-label">{tHero("stats.liveLabel")}</span>
            </div>
            <div className="stat">
              <span className="stat-number">{tHero("stats.infinite")}</span>
              <span className="stat-label">{tHero("stats.infiniteLabel")}</span>
            </div>
          </div>
        </motion.div>
      </div>
      <div className="hero-visual">
        <div className="hero-mockup">
          <div className="floating-card card-1 ai-generated">
            <div className="ai-glow"></div>
            <div className="social-post-preview">
              <div className="post-header">
                <div className="post-avatar">
                  <div className="ai-pulse"></div>
                </div>
                <div className="post-meta">
                  <span className="post-username">
                    {tHero("mockup.card1.username")}
                  </span>
                  <span className="post-time">
                    {tHero("mockup.card1.time")} •{" "}
                    <span className="ai-subtle">AI</span>
                  </span>
                </div>
              </div>
              <div className="post-image-container">
                <img
                  className="post-image"
                  src="https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=400&h=300&fit=crop"
                  alt={tHero("mockup.card1.imageAlt")}
                />
              </div>
              <p className="post-text">{tHero("mockup.card1.text")}</p>
              <div className="post-engagement">
                <span>❤️ {tHero("mockup.card1.likes")}</span>
                <span>💬 {tHero("mockup.card1.comments")}</span>
                <span>🔄 {tHero("mockup.card1.shares")}</span>
              </div>
            </div>
            <div className="ai-shimmer"></div>
          </div>
          <div className="floating-card card-2 ai-generated">
            <div className="ai-glow"></div>
            <div className="social-post-preview">
              <div className="post-header">
                <div className="post-avatar foodie">
                  <div className="ai-pulse"></div>
                </div>
                <div className="post-meta">
                  <span className="post-username">
                    {tHero("mockup.card2.username")}
                  </span>
                  <span className="post-time">
                    {tHero("mockup.card2.time")} •{" "}
                    <span className="ai-subtle">AI</span>
                  </span>
                </div>
              </div>
              <div className="post-image-container">
                <img
                  className="post-image"
                  src="https://images.unsplash.com/photo-1565299624946-b28f40a0ae38?w=400&h=300&fit=crop"
                  alt={tHero("mockup.card2.imageAlt")}
                />
              </div>
              <p className="post-text">{tHero("mockup.card2.text")}</p>
              <div className="post-engagement">
                <span>❤️ {tHero("mockup.card2.likes")}</span>
                <span>💬 {tHero("mockup.card2.comments")}</span>
                <span>🔄 {tHero("mockup.card2.shares")}</span>
              </div>
            </div>
            <div className="ai-shimmer"></div>
          </div>
        </div>
      </div>
    </section>
  );
}
