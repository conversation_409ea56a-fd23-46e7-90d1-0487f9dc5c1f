"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import { useTranslations } from "next-intl";

export default function FeaturesSection() {
  const tFeatures = useTranslations("Features");
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 60,
      scale: 0.8,
      rotateX: 15,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      rotateX: 0,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.1, 0.35, 1] as const,
        type: "spring" as const,
        stiffness: 100,
        damping: 15,
      },
    },
  };

  const cardHoverVariants = {
    hover: {
      y: -8,
      scale: 1.02,
      rotateX: -2,
      rotateY: 2,
      transition: {
        duration: 0.3,
        ease: "easeOut" as const,
      },
    },
    tap: {
      scale: 0.98,
      transition: {
        duration: 0.1,
      },
    },
  };

  return (
    <motion.section
      className="py-24 relative bg-bg-secondary w-full overflow-hidden"
      ref={ref}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="max-w-6xl mx-auto px-5">
        <div className="text-center mb-20">
          <h2 className="text-3xl lg:text-5xl font-bold text-white mb-6 leading-tight">
            {tFeatures("sectionTitle", {
              title: tFeatures("photoToPost"),
            })
              .split(tFeatures("photoToPost"))
              .map((part, index) =>
                index === 0 ? (
                  part
                ) : (
                  <span key={index}>
                    <span className="gradient-text">
                      {tFeatures("photoToPost")}
                    </span>
                    {part}
                  </span>
                )
              )}
          </h2>
          <p className="text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed">
            {tFeatures("sectionSubtitle")}
          </p>
        </div>

        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto overflow-hidden"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Camera Roll Feature - PRIMARY */}
          <motion.div
            className="bg-gradient-to-br from-red-500/10 to-purple-600/10 border border-red-500/20 rounded-2xl p-8 hover:border-red-500/40 transition-all duration-300 relative overflow-hidden group"
            variants={cardVariants}
            whileHover={cardHoverVariants.hover}
            whileTap={cardHoverVariants.tap}
            style={{
              transformStyle: "preserve-3d",
              perspective: 1000,
            }}
          >
            {/* 背景光效 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-red-500/5 to-purple-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={{ scale: 0.8 }}
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.6 }}
            />

            {/* 浮动粒子效果 */}
            <motion.div
              className="absolute top-4 right-4 w-2 h-2 bg-red-400 rounded-full opacity-60"
              initial={{ y: 0, opacity: 0.6 }}
              animate={{
                y: [0, -10, 0],
                opacity: [0.6, 1, 0.6],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              className="absolute bottom-6 left-6 w-1 h-1 bg-purple-400 rounded-full opacity-40"
              initial={{ y: 0, x: 0, opacity: 0.4 }}
              animate={{
                y: [0, -8, 0],
                x: [0, 4, 0],
                opacity: [0.4, 0.8, 0.4],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 0.5,
              }}
            />

            <motion.div
              className="w-16 h-16 bg-gradient-to-br from-red-500/20 to-purple-600/20 rounded-xl flex items-center justify-center mb-6 relative z-10"
              initial={{ scale: 1, rotate: 0 }}
              whileHover={{
                scale: 1.1,
                rotate: 5,
                boxShadow: "0 10px 30px rgba(255, 46, 77, 0.3)",
              }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                className="relative w-10 h-8"
                whileHover={{ scale: 1.1 }}
                transition={{ duration: 0.2 }}
              >
                <motion.div
                  className="w-full h-full bg-white rounded-sm"
                  whileHover={{
                    boxShadow: "0 4px 15px rgba(255, 255, 255, 0.3)",
                  }}
                />
                <motion.div
                  className="absolute top-1 left-1 w-3 h-3 bg-gray-800 rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                <motion.div
                  className="absolute top-0 right-0 w-1 h-1 bg-yellow-400 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [1, 0.5, 1],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
              </motion.div>
            </motion.div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("step1.title")}
            </h3>
            <p className="text-text-secondary mb-6 leading-relaxed">
              {tFeatures("step1.description")}
            </p>
            <ul className="space-y-3">
              <li className="flex items-center text-text-secondary text-sm">
                <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-3"></span>
                {tFeatures("step1.features.0")}
              </li>
              <li className="flex items-center text-text-secondary text-sm">
                <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-3"></span>
                {tFeatures("step1.features.1")}
              </li>
              <li className="flex items-center text-text-secondary text-sm">
                <span className="w-1.5 h-1.5 bg-red-500 rounded-full mr-3"></span>
                {tFeatures("step1.features.2")}
              </li>
            </ul>
          </motion.div>

          {/* Persona Feature */}
          <motion.div
            className="bg-gradient-to-br from-purple-500/10 to-blue-600/10 border border-purple-500/20 rounded-2xl p-8 hover:border-purple-500/40 transition-all duration-300 relative overflow-hidden group"
            variants={cardVariants}
            whileHover={cardHoverVariants.hover}
            whileTap={cardHoverVariants.tap}
            style={{
              transformStyle: "preserve-3d",
              perspective: 1000,
            }}
          >
            {/* 背景光效 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={{ scale: 0.8 }}
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.6 }}
            />

            {/* 浮动粒子效果 */}
            <motion.div
              className="absolute top-4 right-4 w-2 h-2 bg-purple-400 rounded-full opacity-60"
              initial={{ y: 0, opacity: 0.6 }}
              animate={{
                y: [0, -12, 0],
                opacity: [0.6, 1, 0.6],
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />

            <motion.div
              className="w-16 h-16 bg-gradient-to-br from-purple-500/20 to-blue-600/20 relative rounded-xl flex items-center justify-center mb-6"
              whileHover={{
                scale: 1.1,
                rotate: -5,
                boxShadow: "0 10px 30px rgba(124, 58, 237, 0.3)",
              }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex gap-1 w-full justify-center relative">
                <motion.div
                  className="w-3 h-3 bg-gradient-to-br from-red-400 to-red-600 rounded-full"
                  animate={{
                    scale: [1, 1.3, 1],
                    y: [0, -2, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0,
                  }}
                />
                <motion.div
                  className="w-3 h-3 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full"
                  animate={{
                    scale: [1, 1.3, 1],
                    y: [0, -2, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.3,
                  }}
                />
                <motion.div
                  className="w-3 h-3 bg-gradient-to-br from-cyan-400 to-cyan-600 rounded-full"
                  animate={{
                    scale: [1, 1.3, 1],
                    y: [0, -2, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.6,
                  }}
                />
              </div>
            </motion.div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("step2.title")}
            </h3>
            <p className="text-text-secondary mb-6 leading-relaxed">
              {tFeatures("step2.description")}
            </p>
            <ul className="space-y-3">
              <li className="flex items-center text-text-secondary text-sm">
                <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3"></span>
                {tFeatures("step2.features.0")}
              </li>
              <li className="flex items-center text-text-secondary text-sm">
                <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3"></span>
                {tFeatures("step2.features.1")}
              </li>
              <li className="flex items-center text-text-secondary text-sm">
                <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3"></span>
                {tFeatures("step2.features.2")}
              </li>
            </ul>
          </motion.div>

          {/* Trend Detection */}
          <motion.div
            className="bg-gradient-to-br from-cyan-500/10 to-blue-600/10 border border-cyan-500/20 rounded-2xl p-8 hover:border-cyan-500/40 transition-all duration-300 relative overflow-hidden group"
            variants={cardVariants}
            whileHover={cardHoverVariants.hover}
            whileTap={cardHoverVariants.tap}
            style={{
              transformStyle: "preserve-3d",
              perspective: 1000,
            }}
          >
            {/* 背景光效 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-cyan-500/5 to-blue-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={{ scale: 0.8 }}
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.6 }}
            />

            {/* 浮动粒子效果 */}
            <motion.div
              className="absolute top-4 right-4 w-2 h-2 bg-cyan-400 rounded-full opacity-60"
              initial={{ y: 0, x: 0, opacity: 0.6 }}
              animate={{
                y: [0, -10, 0],
                x: [0, 5, 0],
                opacity: [0.6, 1, 0.6],
              }}
              transition={{
                duration: 2.2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />

            <motion.div
              className="w-16 h-16 bg-gradient-to-br from-cyan-500/20 to-blue-600/20 rounded-xl flex items-center justify-center mb-6 relative"
              whileHover={{
                scale: 1.1,
                rotate: 3,
                boxShadow: "0 10px 30px rgba(6, 182, 212, 0.3)",
              }}
              transition={{ duration: 0.3 }}
            >
              <div className="relative w-8 h-8">
                {/* 趋势线动画 */}
                <motion.div
                  className="absolute top-0 left-0 w-6 h-0.5 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full"
                  animate={{
                    scaleX: [0.5, 1.2, 0.8, 1],
                    opacity: [0.7, 1, 0.8, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                <motion.div
                  className="absolute top-2 left-1 w-5 h-0.5 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-full"
                  animate={{
                    scaleX: [0.8, 1, 1.1, 0.9],
                    opacity: [0.8, 1, 0.7, 1],
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.3,
                  }}
                />
                <motion.div
                  className="absolute top-4 left-0.5 w-7 h-0.5 bg-gradient-to-r from-cyan-500 to-blue-400 rounded-full"
                  animate={{
                    scaleX: [1, 0.7, 1.3, 1],
                    opacity: [1, 0.6, 1, 0.8],
                  }}
                  transition={{
                    duration: 1.8,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.6,
                  }}
                />

                {/* 趋势点动画 */}
                <motion.div
                  className="absolute top-0 right-0 w-1.5 h-1.5 bg-cyan-400 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [1, 0.7, 1],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                <motion.div
                  className="absolute top-2 right-1 w-1.5 h-1.5 bg-blue-400 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [1, 0.7, 1],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5,
                  }}
                />
                <motion.div
                  className="absolute top-4 right-0.5 w-1.5 h-1.5 bg-cyan-500 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [1, 0.7, 1],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1,
                  }}
                />
              </div>
            </motion.div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("step3.title")}
            </h3>
            <p className="text-text-secondary mb-6 leading-relaxed">
              {tFeatures("step3.description")}
            </p>
          </motion.div>

          {/* AI Knowledge Integration */}
          <motion.div
            className="bg-gradient-to-br from-green-500/10 to-emerald-600/10 border border-green-500/20 rounded-2xl p-8 hover:border-green-500/40 transition-all duration-300 relative overflow-hidden group"
            variants={cardVariants}
            whileHover={cardHoverVariants.hover}
            whileTap={cardHoverVariants.tap}
            style={{
              transformStyle: "preserve-3d",
              perspective: 1000,
            }}
          >
            {/* 背景光效 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={{ scale: 0.8 }}
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.6 }}
            />

            {/* 知识流动粒子 */}
            <motion.div
              className="absolute top-6 right-6 w-1 h-1 bg-green-400 rounded-full opacity-60"
              initial={{ y: 0, x: 0, opacity: 0.6 }}
              animate={{
                y: [0, -15, 0],
                x: [0, -8, 0],
                opacity: [0.6, 1, 0.6],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />

            <motion.div
              className="w-16 h-16 bg-gradient-to-br from-green-500/20 to-emerald-600/20 rounded-xl flex items-center justify-center mb-6 relative"
              whileHover={{
                scale: 1.1,
                rotate: -3,
                boxShadow: "0 10px 30px rgba(34, 197, 94, 0.3)",
              }}
              transition={{ duration: 0.3 }}
            >
              <div className="relative w-10 h-10">
                {/* 知识节点 */}
                <motion.div
                  className="absolute top-0 left-0 w-2 h-2 bg-green-400 rounded-full"
                  animate={{
                    scale: [1, 1.4, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                <motion.div
                  className="absolute top-0 right-0 w-2 h-2 bg-emerald-400 rounded-full"
                  animate={{
                    scale: [1, 1.4, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.3,
                  }}
                />
                <motion.div
                  className="absolute bottom-0 left-0 w-2 h-2 bg-teal-400 rounded-full"
                  animate={{
                    scale: [1, 1.4, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.6,
                  }}
                />

                {/* 中心知识核心 */}
                <motion.div
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-3 h-3 bg-gradient-to-br from-green-300 to-emerald-500 rounded-full"
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 180, 360],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />

                {/* 连接线动画 */}
                <motion.div
                  className="absolute top-1 left-1 w-6 h-0.5 bg-gradient-to-r from-green-400 to-transparent rounded-full origin-left"
                  animate={{
                    scaleX: [0, 1, 0],
                    opacity: [0, 1, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5,
                  }}
                  style={{ transform: "rotate(45deg)" }}
                />
                <motion.div
                  className="absolute top-1 right-1 w-6 h-0.5 bg-gradient-to-l from-emerald-400 to-transparent rounded-full origin-right"
                  animate={{
                    scaleX: [0, 1, 0],
                    opacity: [0, 1, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1,
                  }}
                  style={{ transform: "rotate(-45deg)" }}
                />
              </div>
            </motion.div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("smartKnowledge.title")}
            </h3>
            <p className="text-text-secondary mb-6 leading-relaxed">
              {tFeatures("smartKnowledge.description")}
            </p>
          </motion.div>

          {/* Trending Topics */}
          <motion.div
            className="bg-gradient-to-br from-orange-500/10 to-red-600/10 border border-orange-500/20 rounded-2xl p-8 hover:border-orange-500/40 transition-all duration-300 relative overflow-hidden group"
            variants={cardVariants}
            whileHover={cardHoverVariants.hover}
            whileTap={cardHoverVariants.tap}
            style={{
              transformStyle: "preserve-3d",
              perspective: 1000,
            }}
          >
            {/* 背景光效 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-orange-500/5 to-red-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={{ scale: 0.8 }}
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.6 }}
            />

            {/* 病毒式传播粒子 */}
            <motion.div
              className="absolute top-4 right-4 w-2 h-2 bg-orange-400 rounded-full opacity-60"
              initial={{ scale: 1, opacity: 0.6 }}
              animate={{
                scale: [1, 2, 1],
                opacity: [0.6, 0.2, 0.6],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />

            <motion.div
              className="w-16 h-16 bg-gradient-to-br from-orange-500/20 to-red-600/20 rounded-xl flex items-center justify-center mb-6 relative"
              whileHover={{
                scale: 1.1,
                rotate: 5,
                boxShadow: "0 10px 30px rgba(249, 115, 22, 0.3)",
              }}
              transition={{ duration: 0.3 }}
            >
              <div className="relative w-10 h-10">
                {/* 病毒式气泡 */}
                <motion.div
                  className="absolute top-2 left-2 w-3 h-3 bg-gradient-to-br from-orange-400 to-red-500 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.8, 0.4, 0.8],
                  }}
                  transition={{
                    duration: 1.2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                <motion.div
                  className="absolute top-0 right-1 w-2 h-2 bg-gradient-to-br from-red-400 to-orange-500 rounded-full"
                  animate={{
                    scale: [1, 1.8, 1],
                    opacity: [0.7, 0.3, 0.7],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.3,
                  }}
                />
                <motion.div
                  className="absolute bottom-1 left-0 w-2.5 h-2.5 bg-gradient-to-br from-orange-500 to-red-400 rounded-full"
                  animate={{
                    scale: [1, 1.6, 1],
                    opacity: [0.9, 0.4, 0.9],
                  }}
                  transition={{
                    duration: 1.8,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.6,
                  }}
                />

                {/* 中心脉冲 */}
                <motion.div
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4 bg-gradient-to-br from-orange-300 to-red-500 rounded-full"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [1, 0.6, 1],
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />

                {/* 扩散波纹 */}
                <motion.div
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 border border-orange-400 rounded-full"
                  animate={{
                    scale: [0.5, 1.5],
                    opacity: [0.8, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeOut",
                  }}
                />
                <motion.div
                  className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 border border-red-400 rounded-full"
                  animate={{
                    scale: [0.5, 1.5],
                    opacity: [0.6, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeOut",
                    delay: 0.5,
                  }}
                />
              </div>
            </motion.div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("alwaysOnTrend.title")}
            </h3>
            <p className="text-text-secondary mb-6 leading-relaxed">
              {tFeatures("alwaysOnTrend.description")}
            </p>
          </motion.div>

          {/* Analytics */}
          <motion.div
            className="bg-gradient-to-br from-indigo-500/10 to-purple-600/10 border border-indigo-500/20 rounded-2xl p-8 hover:border-indigo-500/40 transition-all duration-300 relative overflow-hidden group"
            variants={cardVariants}
            whileHover={cardHoverVariants.hover}
            whileTap={cardHoverVariants.tap}
            style={{
              transformStyle: "preserve-3d",
              perspective: 1000,
            }}
          >
            {/* 背景光效 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 to-purple-600/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
              initial={{ scale: 0.8 }}
              whileHover={{ scale: 1.1 }}
              transition={{ duration: 0.6 }}
            />

            {/* 数据流粒子 */}
            <motion.div
              className="absolute top-4 right-4 w-1 h-1 bg-indigo-400 rounded-full opacity-60"
              initial={{ y: 0, opacity: 0.6 }}
              animate={{
                y: [0, -20, 0],
                opacity: [0.6, 1, 0.6],
              }}
              transition={{
                duration: 2.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />

            <motion.div
              className="w-16 h-16 bg-gradient-to-br from-indigo-500/20 to-purple-600/20 relative rounded-xl flex items-center justify-center mb-6"
              whileHover={{
                scale: 1.1,
                rotate: -2,
                boxShadow: "0 10px 30px rgba(99, 102, 241, 0.3)",
              }}
              transition={{ duration: 0.3 }}
            >
              <div className="relative w-10 h-8">
                {/* 动态图表条 */}
                <motion.div
                  className="absolute bottom-0 left-0 w-1.5 bg-gradient-to-t from-indigo-500 to-indigo-300 rounded-t-sm"
                  animate={{
                    height: ["8px", "16px", "12px", "20px"],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                <motion.div
                  className="absolute bottom-0 left-2.5 w-1.5 bg-gradient-to-t from-purple-500 to-purple-300 rounded-t-sm"
                  animate={{
                    height: ["12px", "24px", "16px", "28px"],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.2,
                  }}
                />
                <motion.div
                  className="absolute bottom-0 left-5 w-1.5 bg-gradient-to-t from-blue-500 to-blue-300 rounded-t-sm"
                  animate={{
                    height: ["16px", "20px", "24px", "18px"],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.4,
                  }}
                />
                <motion.div
                  className="absolute bottom-0 left-7.5 w-1.5 bg-gradient-to-t from-violet-500 to-violet-300 rounded-t-sm"
                  animate={{
                    height: ["20px", "14px", "22px", "16px"],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.6,
                  }}
                />

                {/* 趋势线 */}
                <motion.div
                  className="absolute top-2 left-0 w-full h-0.5"
                  style={{
                    background:
                      "linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.8), rgba(147, 51, 234, 0.8), transparent)",
                  }}
                  animate={{
                    opacity: [0.5, 1, 0.5],
                    scaleX: [0.8, 1.2, 0.8],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />

                {/* 数据点 */}
                <motion.div
                  className="absolute top-1.5 left-1 w-1 h-1 bg-indigo-400 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
                <motion.div
                  className="absolute top-0.5 left-4 w-1 h-1 bg-purple-400 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 0.5,
                  }}
                />
                <motion.div
                  className="absolute top-1 left-7 w-1 h-1 bg-blue-400 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.8, 1, 0.8],
                  }}
                  transition={{
                    duration: 1.5,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1,
                  }}
                />
              </div>
            </motion.div>
            <h3 className="text-xl font-bold text-white mb-4">
              {tFeatures("performanceInsights.title")}
            </h3>
            <p className="text-text-secondary mb-6 leading-relaxed">
              {tFeatures("performanceInsights.description")}
            </p>
          </motion.div>
        </motion.div>
      </div>
    </motion.section>
  );
}
