"use client";

import { motion } from "framer-motion";
import { useInView } from "framer-motion";
import { useRef } from "react";
import { useTranslations } from "next-intl";

export default function ScreenshotsSection() {
  const tScreenshots = useTranslations("Screenshots");
  const tContentGeneration = useTranslations("ContentGeneration");
  const tPersonas = useTranslations("Personas");
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
      },
    },
  };

  return (
    <section className="py-24 relative" ref={ref}>
      <div className="max-w-6xl mx-auto px-5">
        <div className="text-center mb-20">
          <h2 className="text-4xl lg:text-5xl font-black text-white mb-6 leading-tight">
            {tScreenshots("sectionTitle", {
              actionTitle: tScreenshots("actionTitle"),
            })
              .split(tScreenshots("actionTitle"))
              .map((part, index) =>
                index === 0 ? (
                  part
                ) : (
                  <span key={index}>
                    <span className="gradient-text">
                      {tScreenshots("actionTitle")}
                    </span>
                    {part}
                  </span>
                )
              )}
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            {tScreenshots("sectionSubtitle")}
          </p>
        </div>

        <motion.div
          className="space-y-20"
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
        >
          {/* Screenshot 1: Photo Upload */}
          <motion.div
            className="flex flex-col lg:flex-row items-center gap-12"
            variants={itemVariants}
          >
            <div className="flex-1 rounded-2xl">
              <div className="bg-bg-secondary border border-border rounded-xl">
                {/* Header with title and filter tabs */}
                <div className="mb-6 bg-bg-primary">
                  <div className="mb-4 pt-6 pl-6">
                    <h4 className="text-xl font-bold text-white mb-2 flex items-center justify-between pr-4">
                      {tScreenshots("cameraRoll.title")}
                      <span className="text-[12px] text-gray-400 font-normal">
                        {tScreenshots("cameraRoll.tip")}
                      </span>
                    </h4>
                  </div>
                  <div className="flex gap-2 flex-wrap pl-6 pb-4">
                    <div className="px-3 py-1.5 bg-red-500 text-white text-xs font-medium rounded-lg">
                      {tScreenshots("cameraRoll.filters.all")}
                    </div>
                    <div className="px-3 py-1.5 bg-bg-secondary text-gray-300 text-xs font-medium rounded-lg  border-1 border-border hover:bg-bg-tertiary cursor-pointer">
                      {tScreenshots("cameraRoll.filters.fashion")}
                    </div>
                    <div className="px-3 py-1.5 bg-bg-secondary  text-gray-300 text-xs font-medium border-1 border-border rounded-lg hover:bg-bg-tertiary cursor-pointer">
                      {tScreenshots("cameraRoll.filters.lifestyle")}
                    </div>
                    <div className="px-3 py-1.5 bg-bg-secondary text-gray-300 text-xs font-medium border-1 border-border rounded-lg hover:bg-bg-tertiary cursor-pointer">
                      {tScreenshots("cameraRoll.filters.food")}
                    </div>
                    <div className="px-3 py-1.5 bg-bg-secondary  text-gray-300 text-xs font-medium border-1 border-border rounded-lg hover:bg-bg-tertiary cursor-pointer">
                      {tScreenshots("cameraRoll.filters.travel")}
                    </div>
                  </div>
                </div>

                {/* Photo Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-6 pt-0">
                  <div className="bg-bg-secondary rounded-lg border-1 border-[#ff6b7a]">
                    <div className="relative mb-3">
                      <div
                        className="w-full h-24 rounded-ss-lg rounded-se-lg text-2xl flex justify-center items-center"
                        style={{
                          background:
                            "linear-gradient(135deg, #ff6b7a 0%, #c44569 100%)",
                        }}
                      >
                        👗
                      </div>
                    </div>
                    <div className="space-y-2 p-3">
                      <div className="text-text-secondary font-medium text-sm">
                        {tScreenshots("photoDetails.autumnOutfit.filename")}
                      </div>
                      <div className="text-text-muted text-xs leading-relaxed line-clamp-2">
                        {tScreenshots("photoDetails.autumnOutfit.description")}
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-text-muted text-xs">
                          {tScreenshots("photoDetails.autumnOutfit.date")}
                        </span>
                        <span
                          style={{
                            background: "rgba(255, 46, 77, 0.15)",
                          }}
                          className="px-2 py-1  text-accent-red text-xs rounded-full"
                        >
                          {tScreenshots("photoDetails.autumnOutfit.status")}
                        </span>
                      </div>
                      <div className="flex gap-1 flex-wrap">
                        <span className="px-2 py-1 bg-bg-tertiary text-text-secondary text-xs rounded-lg">
                          {tScreenshots("photoDetails.autumnOutfit.tags.0")}
                        </span>
                        <span className="px-2 py-1 bg-bg-tertiary text-text-secondary text-xs rounded-lg">
                          {tScreenshots("photoDetails.autumnOutfit.tags.1")}
                        </span>
                        <span className="px-2 py-1 bg-bg-tertiary text-text-secondary text-xs rounded-lg">
                          {tScreenshots("photoDetails.autumnOutfit.tags.2")}
                        </span>
                      </div>
                      <div className="inline-block px-2 py-1 bg-accent-red mt-1 text-white text-xs rounded-lg">
                        {tScreenshots("photoDetails.autumnOutfit.type")}
                      </div>
                    </div>
                  </div>

                  <div className="bg-bg-secondary rounded-lg pb-4 border border-gray-600 hover:border-gray-500 transition-colors">
                    <div className="relative mb-3">
                      <div
                        className="w-full h-24  rounded-ss-lg rounded-se-lg text-2xl flex justify-center items-center"
                        style={{
                          background:
                            "linear-gradient(135deg, #4834d4 0%, #686de0 100%)",
                        }}
                      >
                        ☕
                      </div>
                    </div>
                    <div className="space-y-2 p-3">
                      <div className="text-text-secondary font-medium text-sm">
                        {tScreenshots("photoDetails.morningCoffee.filename")}
                      </div>
                      <div className="text-text-muted text-xs leading-relaxed">
                        {tScreenshots("photoDetails.morningCoffee.description")}
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-text-muted text-xs">
                          {tScreenshots("photoDetails.morningCoffee.date")}
                        </span>
                        <span className="px-2 py-1 bg-bg-tertiary text-white text-xs rounded-full">
                          {tScreenshots("photoDetails.morningCoffee.status")}
                        </span>
                      </div>
                      <div className="flex gap-1 flex-wrap">
                        <span className="px-2 py-1 bg-bg-tertiary text-gray-200 text-xs rounded-full">
                          {tScreenshots("photoDetails.morningCoffee.tags.0")}
                        </span>
                        <span className="px-2 py-1 bg-bg-tertiary  text-gray-200 text-xs rounded-full">
                          {tScreenshots("photoDetails.morningCoffee.tags.1")}
                        </span>
                      </div>
                      <div className="inline-block px-2 py-1  bg-accent-red mt-1 text-white text-xs rounded-full">
                        {tScreenshots("photoDetails.morningCoffee.type")}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex-1 space-y-6">
              <h3 className="text-2xl font-bold text-white">
                {tScreenshots("startWithPhotos.title")}
              </h3>
              <p className="text-gray-300 leading-relaxed">
                {tScreenshots("startWithPhotos.description")}
              </p>
            </div>
          </motion.div>

          {/* Screenshot 2: Persona Selection */}
          <motion.div
            className="screenshot-item reverse"
            variants={itemVariants}
          >
            <div className="screenshot-image">
              <div className="screenshot-content">
                <div className="demo-persona-manager-compact !px-6">
                  <div className="persona-grid">
                    <div className="persona-card active">
                      <div className="persona-card-avatar fiona">F</div>
                      <h4>{tPersonas("fashionFiona.name")}</h4>
                      <p>{tPersonas("fashionFiona.type")}</p>
                      <div className="persona-knowledge-preview">
                        <div className="knowledge-chip-mini active">
                          {tPersonas("fashionFiona.knowledge.0")}
                        </div>
                        <div className="knowledge-chip-mini active">
                          {tPersonas("fashionFiona.knowledge.1")}
                        </div>
                        <div className="knowledge-chip-mini active">
                          {tPersonas("fashionFiona.knowledge.2")}
                        </div>
                      </div>
                    </div>

                    <div className="persona-card">
                      <div className="persona-card-avatar marcus">M</div>
                      <h4>{tPersonas("chefMarcus.name")}</h4>
                      <p>{tPersonas("chefMarcus.type")}</p>
                      <div className="persona-knowledge-preview">
                        <div className="knowledge-chip-mini">
                          {tPersonas("chefMarcus.knowledge.0")}
                        </div>
                        <div className="knowledge-chip-mini">
                          {tPersonas("chefMarcus.knowledge.1")}
                        </div>
                        <div className="knowledge-chip-mini">
                          {tPersonas("chefMarcus.knowledge.2")}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="persona-detail-summary">
                    <h4
                      style={{
                        color: "var(--text-primary)",
                        marginBottom: "12px",
                        fontSize: "16px",
                      }}
                    >
                      Selected: {tPersonas("fashionFiona.name")}
                    </h4>
                    <p
                      style={{
                        color: "var(--text-secondary)",
                        fontSize: "14px",
                        lineHeight: "1.5",
                        marginBottom: "16px",
                      }}
                    >
                      {tPersonas("fashionFiona.description")}
                    </p>
                    <div className="knowledge-sources-summary">
                      <div className="knowledge-chips">
                        <div className="knowledge-chip active">
                          {tPersonas("fashionFiona.sources.0")}
                        </div>
                        <div className="knowledge-chip active">
                          {tPersonas("fashionFiona.sources.1")}
                        </div>
                        <div className="knowledge-chip active">
                          {tPersonas("fashionFiona.sources.2")}
                        </div>
                        <div className="knowledge-chip">
                          {tPersonas("fashionFiona.sources.3")}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="screenshot-info">
              <h3>{tScreenshots("personasWithKnowledge.title")}</h3>
              <p>{tScreenshots("personasWithKnowledge.description")}</p>
            </div>
          </motion.div>

          {/* Screenshot 3: AI Generation Process */}
          <motion.div className="screenshot-item" variants={itemVariants}>
            <div className="screenshot-image">
              <div className="screenshot-content">
                <div className="demo-content-generation-compact">
                  <div className="ai-generation-showcase">
                    <div className="generation-input-section">
                      <div className="input-header">
                        <span className="input-label">AI Processing</span>
                        <div className="ai-status-indicator">
                          <div className="status-dot active"></div>
                          <span>Active</span>
                        </div>
                      </div>

                      <div className="input-grid">
                        <div className="input-card photo-card">
                          <div className="card-icon">
                            <div
                              className="icon-camera"
                              style={{
                                transform: "scale(0.5)",
                              }}
                            >
                              <div className="camera-lens"></div>
                              <div className="camera-flash"></div>
                            </div>
                          </div>
                          <span className="card-label">Photo</span>
                          <div className="card-content">
                            <div
                              className="photo-thumbnail"
                              style={{
                                background:
                                  "linear-gradient(135deg, #ff6b6b 0%, #ffd93d 100%)",
                              }}
                            ></div>
                          </div>
                        </div>

                        <div className="input-card trend-card">
                          <div className="card-icon">
                            <div
                              className="icon-trend"
                              style={{
                                transform: "scale(0.5)",
                              }}
                            >
                              <div className="trend-line line-1"></div>
                              <div className="trend-line line-2"></div>
                              <div className="trend-line line-3"></div>
                            </div>
                          </div>
                          <span className="card-label">Trends</span>
                          <div className="card-content">
                            <div className="trend-tags">
                              <span className="trend-tag">#FallFashion</span>
                              <span className="trend-tag">#CozyVibes</span>
                            </div>
                          </div>
                        </div>

                        <div className="input-card persona-card">
                          <div className="card-icon">
                            <div
                              className="mini-persona w-6 h-6 rounded-full flex items-center justify-between text-white text-[12px] font-bold"
                              style={{
                                background: " var(--accent-purple)",
                              }}
                            >
                              F
                            </div>
                          </div>
                          <span className="card-label">Persona</span>
                          <div className="card-content">
                            <span className="persona-name">Fashion Fiona</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="ai-processing-divider">
                      <div className="processing-line">
                        <div className="processing-pulse"></div>
                      </div>
                      <div className="ai-badge">
                        <span>AI Magic ✨</span>
                      </div>
                    </div>

                    <div className="generation-output-section">
                      <div className="output-header">
                        <span className="output-label">Generated Post</span>
                        <div className="platform-indicator">
                          <span>🐦 Twitter</span>
                        </div>
                      </div>

                      <div className="generated-post-preview">
                        <div className="mini-post">
                          <div className="mini-post-header">
                            <div
                              className="mini-avatar"
                              style={{
                                background: "var(--accent-purple)",
                              }}
                            >
                              F
                            </div>
                            <span className="mini-username">@fashionfiona</span>
                          </div>
                          <p className="mini-post-text">
                            OMG this autumn look is EVERYTHING! 😍 The oversized
                            knit + ankle boots combo is absolutely perfect...
                          </p>
                          <div className="mini-post-meta">
                            <span>Ready to post</span>
                            <button className="mini-post-button">
                              Publish
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="screenshot-info">
              <h3>{tScreenshots("smartContentGeneration.title")}</h3>
              <p>{tScreenshots("smartContentGeneration.description")}</p>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
}
