# Next.js 多语言应用 (Next.js Multilingual App)

这是一个使用 Next.js 15、TypeScript、Tailwind CSS 和 next-intl 构建的多语言应用程序。

## 🌟 功能特性

- ✅ **Next.js 15** - 使用最新的 App Router
- ✅ **TypeScript** - 完整的类型安全
- ✅ **Tailwind CSS** - 现代化的样式框架
- ✅ **多语言支持** - 支持英语、中文、日语
- ✅ **国际化路由** - 基于 URL 的语言切换
- ✅ **响应式设计** - 适配移动端和桌面端
- ✅ **语言切换器** - 优雅的语言选择组件

## 🚀 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

在浏览器中打开 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
npm run build
npm start
```

## 🌍 支持的语言

- **English** (en) - 英语
- **中文** (zh) - 简体中文
- **日本語** (ja) - 日语

## 📁 项目结构

```
src/
├── app/
│   └── [locale]/          # 多语言路由
│       ├── layout.tsx     # 布局组件
│       ├── page.tsx       # 首页
│       └── about/
│           └── page.tsx   # 关于页面
├── components/
│   ├── Navigation.tsx     # 导航组件
│   └── LanguageSwitcher.tsx # 语言切换器
├── i18n/
│   ├── request.ts         # 国际化请求配置
│   └── routing.ts         # 路由配置
└── middleware.ts          # 中间件
messages/
├── en.json               # 英语翻译
├── zh.json               # 中文翻译
└── ja.json               # 日语翻译
```

## 🔧 技术栈

- **框架**: Next.js 15
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **国际化**: next-intl
- **包管理**: npm

## 📝 使用说明

### 添加新语言

1. 在 `src/i18n/routing.ts` 中添加新的语言代码
2. 在 `messages/` 目录下创建对应的 JSON 文件
3. 在 `LanguageSwitcher.tsx` 中添加新语言选项

### 添加新的翻译

在 `messages/` 目录下的 JSON 文件中添加新的键值对：

```json
{
  "NewSection": {
    "title": "新标题",
    "description": "新描述"
  }
}
```

### 在组件中使用翻译

```tsx
import { useTranslations } from "next-intl";

export default function MyComponent() {
  const t = useTranslations("NewSection");

  return (
    <div>
      <h1>{t("title")}</h1>
      <p>{t("description")}</p>
    </div>
  );
}
```

## 🌐 URL 结构

- `/` - 重定向到默认语言 (英语)
- `/en` - 英语版本
- `/zh` - 中文版本
- `/ja` - 日语版本
- `/en/about` - 英语关于页面
- `/zh/about` - 中文关于页面
- `/ja/about` - 日语关于页面

## 📚 了解更多

- [Next.js 文档](https://nextjs.org/docs)
- [next-intl 文档](https://next-intl-docs.vercel.app/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)

## 🚀 部署

推荐使用 [Vercel](https://vercel.com) 部署，它是 Next.js 的创建者提供的平台。

查看 [Next.js 部署文档](https://nextjs.org/docs/app/building-your-application/deploying) 了解更多详情。
